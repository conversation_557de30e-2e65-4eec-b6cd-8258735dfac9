<template>
  <head-content-layout class="page-wrap">
    <template #header>
      <el-form
        label-width="120px"
        :inline="false"
        class="demo-form-inline"
        label-position="right"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label-width="0px">
              <el-input
                v-model.trim="form.workOrderNo"
                placeholder="工单编号"
                style="width: 100%"
                clearable
              >
                <el-button
                  slot="append"
                  type="primary"
                  style="
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.6px;
                    position: relative;
                  "
                  class="search-input-button"
                  @click="onSearch"
                  >查询</el-button
                >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label-width="0px">
              <el-button
                type="text"
                style="color: #606266"
                v-if="!showMoreSearch"
                @click="onSwitchMoreSearch"
                >高级查询
                <i class="el-icon-caret-bottom" style="color: #b50b14"></i
              ></el-button>
              <el-button
                type="text"
                style="color: #606266"
                v-else
                @click="onSwitchMoreSearch"
                >高级查询<i
                  class="el-icon-caret-top"
                  style="color: #b50b14"
                ></i>
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>

<!--        高级查询-->
        <el-row
          type="flex"
          v-show="showMoreSearch"
          :gutter="20"
          style="flex-wrap: wrap"
        >
          <el-col :span="8">
            <el-form-item label="工单主题：">
              <el-input
                v-model.trim="form.workOrderSubject"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="主送：">
              <el-input
                v-model.trim="form.mainRecipient"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工单状态：">
              <el-select
                v-model="form.workOrderStatus"
                placeholder="请选择工单状态"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in workOrderStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="省份：">
              <el-select
                v-model="form.province"
                placeholder="请选择省份"
                clearable
                style="width: 100%"
                @change="onProvinceChange"
              >
                <el-option
                  v-for="item in provinceOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="地市：">
              <el-select
                v-model="form.city"
                placeholder="请选择地市"
                clearable
                style="width: 100%"
                :disabled="!form.province"
              >
                <el-option
                  v-for="item in cityOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="建单时间：">
              <el-date-picker
                v-model="form.createTimeRange"
                type="datetimerange"
                range-separator="-"
                :start-placeholder="startTime"
                :end-placeholder="endTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
<!--          <el-col :span="4">-->
<!--            <el-button type="primary" @click="seniorQuery">筛选</el-button>-->
<!--          </el-col>-->

          <el-col :span="24" style="text-align: right; padding-right: 10px">
            <el-form-item label="" label-width="0">
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
<!--              <el-button type="primary" @click="onResetForm">重置</el-button>-->
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>

    <template #contentHeader>
      <el-row
        ref="contentHeader"
        :gutter="20"
        class="content-header"
        v-loading="tableLoading"
        element-loading-spinner=" "
      >
        <el-col :xs="24" :sm="12" :md="14" :lg="18">
<!--          <filter-total-->
<!--            v-loading="tabMenuLoading"-->
<!--            element-loading-text="数据加载中..."-->
<!--            element-loading-spinner="el-icon-loading"-->
<!--            element-loading-background="rgba(0, 0, 0, 0.8)"-->
<!--            :value.sync="form.type"-->
<!--            :filters="filterList"-->
<!--            :totals="filterTotals"-->
<!--            @onConHeadFilterChange="onConHeadFilterChange"-->
<!--          ></filter-total>-->
          <div class="filter-total">
            <span
              class="filter-total-item"
              :class="{ active: true }"
            >共建共享通知</span>
          </div>
        </el-col>
        <el-col  :xs="24" :sm="24" :md="10" :lg="6"  style="text-align: right;">
          <div>
            <el-button type="primary" @click="getTableData('senior')"
            >刷新</el-button
            >
          </div>
        </el-col>
<!--        <el-col :xs="24" :sm="24" :md="10" :lg="6" style="text-align: right">-->
<!--          <div>-->
<!--            <el-button type="primary" @click="getTableData('senior')"-->
<!--              >刷新</el-button-->
<!--            >-->
<!--          </div>-->
<!--        </el-col>-->
      </el-row>
    </template>

    <template #table>
      <el-table
        ref="table"
        :data="tableData"
        :border="false"
        stripe
        @selection-change="onSelectionChange"
        height="100%"
        v-loading="tableLoading"
      >
        <el-table-column key="1" label="工单类别" width="120px">
          <template slot-scope="scope">
            共建共享
          </template>
        </el-table-column>
        <el-table-column key="2" label="工单编号" width="200px">
          <template slot-scope="scope">
            <el-button
              type="text"
              class="sheetNo_style"
              @click="toDoDetail(scope.row)"
            >{{ scope.row.workOrderNo || scope.row.sheetNo }}</el-button
            >
          </template>
        </el-table-column>
        <el-table-column key="3" prop="workOrderSubject" label="工单主题" min-width="250px"></el-table-column>
        <el-table-column key="4" prop="createTime" label="建单时间" width="180px"></el-table-column>
        <el-table-column key="5" prop="province" label="省份" width="120px"></el-table-column>
        <el-table-column key="6" prop="city" label="地市" width="120px"></el-table-column>
        <el-table-column key="7" label="故障专业" width="100px">
          <template slot-scope="scope">
            无线网
          </template>
        </el-table-column>
        <el-table-column key="8" prop="mainRecipient" label="主送人" width="120px"></el-table-column>
        <el-table-column key="9" prop="workOrderStatus" label="工单状态" width="120px"></el-table-column>
      </el-table>
    </template>
    <template #pagination>
      <pagination
        ref="pagination"
        :total="form.total"
        :page.sync="form.pageNum"
        :limit.sync="form.pageSize"
        @change="getTableData('senior')"
        v-loading="tableLoading"
        element-loading-spinner=" "
      />
    </template>
<!--    <template #dialog>-->
<!--      <work-order-export-->
<!--        ref="dataExport"-->
<!--        :visible.sync="dataExportDialogVisible"-->
<!--        :order-type="form.type"-->
<!--        :province="provinceName"-->
<!--        :areaType="areaType"-->
<!--      ></work-order-export>-->
<!--      <dia-orgs-user-tree-->
<!--        :title="diaPeople.title"-->
<!--        :visible.sync="diaPeople.visible"-->
<!--        :showOrgsTree="diaPeople.showOrgsTree"-->
<!--        @on-save="onSavePeople"-->
<!--      />-->
<!--    </template>-->
  </head-content-layout>
</template>

<script>
import { mapGetters } from "vuex";
import HeadContentLayout from "./components/HeadContentLayout.vue";
import Pagination from "./components/Pagination.vue";
import DictSelect from "./components/DictSelect.vue";
import { apiShareQueryAll } from "./api/WorkOrderList";
import FilterTotal from "./components/FilterTotal.vue";
import WorkOrderExport from "./components/WorkOrderExport.vue";
import DiaOrgsUserTree from "./components/DiaOrgsUserTree.vue";
import {
  apiUserInfoByUserNames,
  apiGetWoListProOrCity,
} from "./api/CommonApi";
// import { apiGetFaultArea } from "./workOrderWaitDetail/api/CommonApi";
import moment from "moment";
export default {
  name: "WorkOrderList",
  components: {
    HeadContentLayout,
    Pagination,
    DictSelect,
    FilterTotal,
    WorkOrderExport,
    DiaOrgsUserTree,
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    return {
      // 查询参数
      btnLoading: false,
      form: {
        workOrderNo: "", // 工单编号
        workOrderSubject: "", // 工单主题
        mainRecipient: "", // 主送
        workOrderStatus: "", // 工单状态
        province: "", // 省份
        city: "", // 地市
        createTimeRange: [], // 建单时间范围
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      // emergencyDictId:'10001',
      // statusDictId: "30009",
      provinceRangeTime: [], //省份建单时间
      startTime: moment().subtract(30, "days").format("YYYY-MM-DD 00:00:00"),
      endTime: moment().format("YYYY-MM-DD 23:59:59"),
      showMoreSearch: false,
      // moreSearchColAttrs: Object.freeze({
      //   xs: 24,
      //   sm: 12,
      //   md: 12,
      //   lg: 8,
      //   offset: 0,
      // }),
      filterList: [],
      // tabMenuLoading: false,
      // filterTotals: {
      //   ggw: 0,
      //   fiveGc: 0,
      //   ywpt: 0,
      //   gt: 0,
      //   yzy: 0,
      //   ITy: 0,
      //   txy: 0,
      //   province: 0,
      // },
      tableLoading: false,
      tableData: [],
      multipleSelections: [],
      dataExportDialogVisible: false,
      // 工单状态选项
      workOrderStatusOptions: [
        { label: "待受理", value: "待受理" },
        { label: "处理中", value: "处理中" },
        { label: "待定性", value: "待定性" },
        { label: "待定性审核", value: "待定性审核" },
        { label: "挂起", value: "挂起" },
        { label: "待确认", value: "待确认" },
        { label: "已归档", value: "已归档" },
        { label: "已回退", value: "已回退" }
      ],
      // diaPeople: {
      //   visible: false,
      //   title: "",
      //   saveName: "",
      //   saveTitleMap: {
      //     builderDetermine: "建单人选择",
      //     agentDetermine: "主送选择",
      //   },
      //   showOrgsTree: true,
      //   showOrgsTreeMap: {
      //     builderDetermine: false,
      //   },
      // },
      createUserIdArr: [],
      faultRegionOptions: [],
      userData: null,
      provinceOptions: [],
      cityOptions: [],
      regionOptions: [],
      areaCode: null,
      provinceName: null,
      areaType: "",
    };

  },
  created() {
    this.userData = JSON.parse(this.userInfo.attr2);
  },
  mounted() {
    this.getAreaInfo().then(() => {
      this.getProvinceOptions();
    });
    this.onSearch();
  },
  methods: {
    getProvinceOptions() {
      if (this.userData.category == "PRO") {
        this.provinceOptions.push({
          id: "1",
          code: null,
          name: this.provinceName,
          decr: null,
        });
      } else {
        let param = {
          areaCode: "",
          type: "",
        };
        if (this.userData.orgInfo.fullOrgName.indexOf("联通国际公司") != -1) {
          param.type = "international";
        } else {
          param.type = "province";
        }
        apiGetWoListProOrCity(param)
          .then(res => {
            if (res.status == "0") {
              this.provinceOptions = res?.data ?? [];
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    },
    // professionalSum(param) {
    //   if (this.filterList.length == 0) this.tabMenuLoading = true;
    //   apicountAllGrp(param)
    //     .then(res => {
    //       if (res.status == "0") {
    //         let _this = this;
    //         let v = res?.data?.rows ?? [];
    //         let filList = [];
    //         for (let i = 0; i < v.length; i++) {
    //           filList.push({
    //             label: v[i].professionalType,
    //             value: this.getLabelValue(v[i].professionalType),
    //             professionalType: v[i].professionalTypeCode,
    //             networkTypeTop: v[i].networkTypeTop,
    //           });
    //         }
    //         this.filterList = filList;
    //         this.tabMenuLoading = false;
    //         for (let i = 0; i < v.length; i++) {
    //           filList.map(item => {
    //             if (item.professionalType == v[i].professionalTypeCode) {
    //               _this.filterTotals[item.value] = v[i].sum;
    //             }
    //           });
    //         }
    //       } else {
    //         this.tabMenuLoading = false;
    //       }
    //     })
    //     .catch(err => {
    //       console.log(err);
    //       this.this.tabMenuLoading = false;
    //     });
    // },
    // getLabelValue(val) {
    //   switch (val) {
    //     case "骨干网":
    //       return "ggw";
    //     case "IT云设备":
    //       return "ITy";
    //     case "通信云":
    //       return "txy";
    //     case "省分":
    //       return "province";
    //     case "高铁督办":
    //       return "gt";
    //     case "省分通用":
    //       return "sfty";
    //     case "集团通用":
    //       return "commonFlow";
    //   }
    // },
    // setType(newVal) {
    //   if (newVal == "传输网") {
    //     this.form.type = "ggw";
    //   } else if (newVal == "IT云设备") {
    //     this.form.type = "ITy";
    //   } else if (newVal == "通信云") {
    //     this.form.type = "txy";
    //   } else if (newVal == "省分") {
    //     this.form.type = "province";
    //   }else if(newVal == "集团通用"){
    //     this.form.type = "commonFlow";
    //   } else {
    //     this.form.type = "";
    //   }
    // },
    // getType() {
    //   let type = "";
    //   if (this.form.specialty == "传输网") {
    //     type = "3";
    //   } else if (this.form.specialty == "IT云设备") {
    //     type = "23";
    //   } else if (this.form.specialty == "通信云") {
    //     type = "22";
    //   }else if (this.form.specialty == "高铁督办") {
    //     type = "";
    //   }else if (this.form.specialty == "省分通用") {
    //     type = "";
    //   }else if (this.form.specialty == "集团通用") {
    //     type = "";
    //   } else {
    //     type = this.form.specialty;
    //   }
    //   return type;
    // },
    onSwitchMoreSearch() {
      this.showMoreSearch = !this.showMoreSearch;
    },
    //获取区域信息
    getAreaInfo() {
      return new Promise(resolve => {
        let param = {
          userName: this.userData.userName,
        };
        apiUserInfoByUserNames(param)
          .then(res => {
            if (res.status == "0") {
              this.areaCode = res?.data?.data?.[0]?.areaCode ?? "";
              this.provinceName = res?.data?.data?.[0].areaName ?? "";
              this.areaType = res?.data?.data?.[0].areaType ?? "";
              resolve("success");
            }
          })
          .catch(error => {
            console.log(error);
          });
      });
    },
    // 省份变化时联动地市
    onProvinceChange() {
      this.form.city = "";
      this.cityOptions = [];
      if (!this.form.province) {
        return;
      }

      let selectedProvince = this.provinceOptions.find(
        item => item.name === this.form.province
      );
      if (!selectedProvince) {
        return;
      }

      let param = {
        areaCode: selectedProvince.id,
        type: "region",
      };
      apiGetWoListProOrCity(param)
        .then(res => {
          if (res.status == "0") {
            this.cityOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // getFaultAreaOptions(areaCode) {
    //   if (this.userData.category == "PRO") {
    //     let param = {
    //       areaCode: this.areaCode,
    //       type: "region",
    //     };
    //     apiGetWoListProOrCity(param)
    //       .then(res => {
    //         if (res.status == "0") {
    //           this.faultRegionOptions = res?.data ?? [];
    //         }
    //       })
    //       .catch(error => {
    //         console.log(error);
    //       });
    //   } else if (this.userData.category == "CITY") {
    //     let param = {
    //       areaCode: this.areaCode,
    //     };
    //     apiGetRegionInfo(param)
    //       .then(res => {
    //         if (res.status == "0") {
    //           this.faultRegionOptions = res?.data ?? [];
    //           this.form.faultRegion = this.faultRegionOptions[0].name;
    //         }
    //       })
    //       .catch(error => {
    //         console.log(error);
    //       });
    //   }
    // },

    onSearch() {
      this.form.pageNum = 1;
      this.form.type = "";
      // if (this.form.type != "gt") {
      //   this.setType(this.form.specialty);
      // }
      this.getTableData("simple");
    },
    seniorQuery() {
      this.form.pageNum = 1;

      // if (this.form.type == "ggw" || this.form.type == "ITy" || this.form.type == "txy") {
      //   this.setType(this.form.specialty);
      // }
      this.getTableData("senior");
    },
    onResetForm() {
      this.createUserIdArr = [];
      this.form = {
        workOrderNo: "", // 工单编号
        workOrderSubject: "", // 工单主题
        mainRecipient: "", // 主送
        workOrderStatus: "", // 工单状态
        province: "", // 省份
        city: "", // 地市
        createTimeRange: [], // 建单时间范围
        pageNum: 1,
        pageSize: 10,
        total: 0,
      };
      this.cityOptions = [];
    },
    onHeadFilterChange(val = "") {
      this.form.type = val;
    },

    getTableData(type) {
      this.tableLoading = true;
      let seniorParam = {
        startTime: this?.form?.createTimeRange?.[0] ?? this.startTime,
        endTime: this?.form?.createTimeRange?.[1] ?? this.endTime,
        workOrderNo: this.form.workOrderNo, // 工单编号
        workOrderSubject: this.form.workOrderSubject, // 工单主题
        mainRecipient: this.form.mainRecipient, // 主送
        workOrderStatus: this.form.workOrderStatus, // 工单状态
        province: this.form.province, // 省份
        city: this.form.city // 地市
      };

      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        param1: JSON.stringify(seniorParam),
      };
      apiShareQueryAll(param)
        .then(res => {
          if (res.status == "0") {
            this.tableData = res?.data?.rows ?? [];
            this.form.total = res?.data?.totalElements ?? 0;

            // this.professionalSum(param);
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },

    onSelectionChange(selection) {
      // this.multipleSelections = selection;
    },
    indexMethod(index) {
      const currentPage = this.form.pageNum;
      const pageSize = this.form.pageSize;
      return (currentPage - 1) * pageSize + (index + 1);
    },
    toDoDetail(data) {
      this.$router.push({
        name: "sharelist_detail",
        query: {
          sheetNo: data.sheetNo,
        },
      });
    },
    stitchingAlgorithm(orgName, userName) {
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName + "," + userName;
      } else {
        if (orgName.length !== 0) {
          return orgName;
        } else if (userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "./workOrderWaitDetail/assets/common.scss";
.filter-total {
  ::v-deep .el-loading-mask .el-loading-spinner {
    text-align: left;
    line-height: 32px;
    margin-left: 150px;
  }
  ::v-deep .el-loading-text {
    display: inline-block;
    margin-left: 10px;
  }
}
.descStyle {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.acceptStyle {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.filter-total {
  .filter-total-item {
    display: inline-block;
    padding: 7px 15px;
    font-size: 13px;
    cursor: pointer;
    &.active {
      border-radius: 2px;
      @include themify() {
        background-color: themed("$--color-primary");
        color: themed("$--color-white");
      }
    }
  }
}

</style>
