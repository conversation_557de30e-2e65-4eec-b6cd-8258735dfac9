# 工单共享列表页面调整测试

## 修改内容总结

### 1. 搜索区域字段调整
- ✅ 工单编号（支持模糊匹配）- 替换了原来的"派单方系统单号"
- ✅ 工单主题（支持模糊匹配）- 替换了原来的"工单标题"
- ✅ 建单时间（时间控件）- 保持不变
- ✅ 主送（模糊匹配）- 新增字段
- ✅ 工单状态（下拉框）- 替换了原来的输入框，增加了枚举选项
- ✅ 省份（下拉框，与地市联动）- 新增字段
- ✅ 地市（下拉框，受省份联动控制）- 新增字段

### 2. 表格字段调整
- ✅ 工单类别（固定值：共建共享）- 新增列
- ✅ 工单编号 - 替换了原来的"派单方系统单号"
- ✅ 工单主题 - 替换了原来的"故障标题"
- ✅ 建单时间 - 保持不变
- ✅ 省份 - 新增列
- ✅ 地市 - 新增列
- ✅ 故障专业（固定值：无线网）- 新增列
- ✅ 主送人 - 新增列
- ✅ 工单状态 - 替换了原来的"工单进度情况"

### 3. 数据结构调整
- ✅ 更新了form对象的字段名称
- ✅ 添加了工单状态枚举选项
- ✅ 添加了省份和地市选项数组
- ✅ 更新了API调用参数

### 4. 功能增强
- ✅ 添加了省份地市联动功能
- ✅ 更新了重置表单功能
- ✅ 保持了原有的查询和分页功能

## 工单状态枚举值
- 待受理
- 处理中
- 待定性
- 待定性审核
- 挂起
- 待确认
- 已归档
- 已回退

## 需要注意的事项
1. 后端API需要支持新的字段参数
2. 数据库字段映射需要对应调整
3. 省份地市数据需要通过现有的API获取
4. 工单状态的枚举值需要与后端保持一致

## 代码修改详情

### 模板部分修改
1. **搜索输入框**：将"派单方系统单号"改为"工单编号"，绑定字段从`form.sheetNo`改为`form.workOrderNo`
2. **高级查询区域**：
   - 工单主题：绑定`form.workOrderSubject`
   - 主送：绑定`form.mainRecipient`
   - 工单状态：使用下拉选择，绑定`form.workOrderStatus`
   - 省份：下拉选择，绑定`form.province`，添加`@change="onProvinceChange"`事件
   - 地市：下拉选择，绑定`form.city`，受省份控制

3. **表格列调整**：
   - 新增"工单类别"列，固定显示"共建共享"
   - "工单编号"列，显示`scope.row.workOrderNo || scope.row.sheetNo`
   - "工单主题"列，绑定`workOrderSubject`
   - "建单时间"列，绑定`createTime`
   - 新增"省份"列，绑定`province`
   - 新增"地市"列，绑定`city`
   - 新增"故障专业"列，固定显示"无线网"
   - "主送人"列，绑定`mainRecipient`
   - "工单状态"列，绑定`workOrderStatus`

### 脚本部分修改
1. **数据结构调整**：
   - 更新`form`对象字段名称
   - 添加`workOrderStatusOptions`数组
   - 添加`cityOptions`数组

2. **方法调整**：
   - 恢复`getAreaInfo()`方法
   - 恢复并修改`getProvinceOptions()`方法
   - 新增`onProvinceChange()`方法实现省份地市联动
   - 更新`getTableData()`方法的参数
   - 更新`onResetForm()`方法

3. **导入清理**：
   - 移除未使用的API导入

## 测试建议
1. 测试工单编号的模糊搜索功能
2. 测试工单主题的模糊搜索功能
3. 测试主送的模糊搜索功能
4. 测试工单状态下拉选择功能
5. 测试省份地市联动功能
6. 测试建单时间范围选择功能
7. 测试表格数据显示是否正确
8. 测试分页功能是否正常
9. 测试重置功能是否清空所有字段

## 后端配合事项
1. 确保API接口支持新的查询参数字段名称
2. 确认工单状态枚举值与前端保持一致
3. 确保返回的数据包含新增的字段（province, city, mainRecipient等）
4. 验证省份地市联动API的正确性
